package main

import (
	"context"
	"crypto/tls"
	"fmt" 
	"log"
	"net/http"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"

	"Golang/SystemSupport/internal/config"
	"Golang/SystemSupport/internal/httpserver"
	"Golang/SystemSupport/internal/mqttclient"
	"Golang/SystemSupport/internal/sign"
	"Golang/SystemSupport/internal/store/influx"
	"Golang/SystemSupport/internal/supportsvc"

	mqtt "github.com/eclipse/paho.mqtt.golang"

	// [NEW] Wails
	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/assetserver"
	wruntime "github.com/wailsapp/wails/v2/pkg/runtime"

	// Đừng import "runtime" chuẩn ở đây để tránh nhầm với wruntime.
	// Nếu cần NumCPU, bạn có thể import runtime chuẩn và alias nó là srt:
	// srt "runtime"
)

// querierAdapter adapts influx.Querier to supportsvc.Querier
// (bỏ qua bucket vì Flux tự chỉ ra bucket trong query)
type querierAdapter struct{ q influx.Querier }

func (qa *querierAdapter) Query(ctx context.Context, org, bucket, flux string) ([]map[string]any, error) {
	return qa.q.Query(ctx, org, flux)
}
func (qa *querierAdapter) Close() error { qa.q.Close(); return nil }

func main() {
	// 1) Load config
	cfg, err := config.Load("config.json")
	if err != nil {
		log.Printf("config load warning: %v", err)
	}

	// 2) Signer (giữ nguyên)
	signer, err := sign.NewEd25519SignerFromPEM(sign.DefaultEd25519PrivPEM)
	if err != nil {
		log.Fatalf("init signer error: %v", err)
	}

	// 3) HTTP handler
	handler := httpserver.BuildHandler(signer)

	// 4) TLS
	tlsCfg := httpserver.TLSConfigFromEmbedded()

	// 5) Context + shutdown
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	// 6) MQTT
	mqttHandle, err := mqttclient.Start(ctx, cfg.MQTT)
	if err != nil {
		log.Fatalf("mqtt start error: %v", err)
	}
	if mqttHandle == nil {
		log.Printf("MQTT: disabled or not configured")
	}

	// 7) HTTPS serve
	addr := "127.0.0.1:8443"
	ln, err := tls.Listen("tcp", addr, tlsCfg)
	if err != nil {
		log.Fatalf("listen tls: %v", err)
	}
	srv := &http.Server{Handler: handler, TLSConfig: tlsCfg}
	log.Printf("Go server on https://%s (embedded TLS, ed25519 signing)", addr)
	go func() {
		if err := srv.Serve(ln); err != nil && err != http.ErrServerClosed {
			log.Fatalf("http serve: %v", err)
		}
	}()

	// Influx (tùy chọn)
	var influxQ influx.Querier
	if cfg.Influx.Enable {
		influxQ = influx.NewV2(influx.V2Config{
			URL: cfg.Influx.URL, Token: cfg.Influx.Token, Org: cfg.Influx.Org,
		})
		defer influxQ.Close()
	}

	// Support service + handlers (khi có MQTT)
	var svc *supportsvc.Service
	if mqttHandle != nil {
		var mqttCli mqtt.Client
		if c := mqttHandle.Client(); c != nil {
			mqttCli = c
		} else {
			log.Fatalf("mqtt client is nil")
		}

		handlers := []supportsvc.Handler{}
		if influxQ != nil {
			adapter := &querierAdapter{q: influxQ}
			dbHandler := &supportsvc.DatabaseHandler{
				MQTT:          mqttCli,
				QoS:           0,
				Retain:        false,
				DefaultOrg:    cfg.Influx.Org,
				DefaultBucket: cfg.Influx.DefaultBucket,
				Querier:       adapter,
				// Không giới hạn: không cắt rows, không timeout script (0)
				ScriptTimeout: 0,
				MaxRows:       0,

				Writer: &supportsvc.HTTPLineWriter{ // <--- THÊM
					URL:   cfg.Influx.URL,
					Token: cfg.Influx.Token,
				},
			}
			handlers = append(handlers, dbHandler)
		} else {
			log.Printf("Influx disabled — DatabaseHandler not registered")
		}

		svc = supportsvc.New(mqttCli, supportsvc.Config{
			TopicSupport:  cfg.MQTT.TopicSupport,
			QoS:           0,
			Concurrency:   runtime.NumCPU(),
			PublishRetain: false,
		}, handlers...)

		if err := svc.Start(ctx); err != nil {
			log.Fatalf("support service start error: %v", err)
		}
	}

	<-ctx.Done()
	log.Printf("shutting down...")
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	_ = srv.Shutdown(shutdownCtx)
	if svc != nil {
		svc.Wait()
	}
	if mqttHandle != nil {
		mqttHandle.Close()
	}
	log.Printf("bye.")
}
