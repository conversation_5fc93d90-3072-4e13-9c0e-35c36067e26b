export namespace assetserver {
	
	export class Options {
	    Assets: any;
	    Handler: any;
	
	    static createFrom(source: any = {}) {
	        return new Options(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.Assets = source["Assets"];
	        this.Handler = source["Handler"];
	    }
	}

}

export namespace keys {
	
	export class Accelerator {
	    Key: string;
	    Modifiers: string[];
	
	    static createFrom(source: any = {}) {
	        return new Accelerator(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.Key = source["Key"];
	        this.Modifiers = source["Modifiers"];
	    }
	}

}

export namespace linux {
	
	export class Messages {
	    WebKit2GTKMinRequired: string;
	
	    static createFrom(source: any = {}) {
	        return new Messages(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.WebKit2GTKMinRequired = source["WebKit2GTKMinRequired"];
	    }
	}
	export class Options {
	    Icon: number[];
	    WindowIsTranslucent: boolean;
	    Messages?: Messages;
	    WebviewGpuPolicy: number;
	    ProgramName: string;
	
	    static createFrom(source: any = {}) {
	        return new Options(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.Icon = source["Icon"];
	        this.WindowIsTranslucent = source["WindowIsTranslucent"];
	        this.Messages = this.convertValues(source["Messages"], Messages);
	        this.WebviewGpuPolicy = source["WebviewGpuPolicy"];
	        this.ProgramName = source["ProgramName"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}

}

export namespace mac {
	
	export class AboutInfo {
	    Title: string;
	    Message: string;
	    Icon: number[];
	
	    static createFrom(source: any = {}) {
	        return new AboutInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.Title = source["Title"];
	        this.Message = source["Message"];
	        this.Icon = source["Icon"];
	    }
	}
	export class Preferences {
	    // Go type: u
	    TabFocusesLinks: any;
	    // Go type: u
	    TextInteractionEnabled: any;
	    // Go type: u
	    FullscreenEnabled: any;
	
	    static createFrom(source: any = {}) {
	        return new Preferences(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.TabFocusesLinks = this.convertValues(source["TabFocusesLinks"], null);
	        this.TextInteractionEnabled = this.convertValues(source["TextInteractionEnabled"], null);
	        this.FullscreenEnabled = this.convertValues(source["FullscreenEnabled"], null);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class TitleBar {
	    TitlebarAppearsTransparent: boolean;
	    HideTitle: boolean;
	    HideTitleBar: boolean;
	    FullSizeContent: boolean;
	    UseToolbar: boolean;
	    HideToolbarSeparator: boolean;
	
	    static createFrom(source: any = {}) {
	        return new TitleBar(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.TitlebarAppearsTransparent = source["TitlebarAppearsTransparent"];
	        this.HideTitle = source["HideTitle"];
	        this.HideTitleBar = source["HideTitleBar"];
	        this.FullSizeContent = source["FullSizeContent"];
	        this.UseToolbar = source["UseToolbar"];
	        this.HideToolbarSeparator = source["HideToolbarSeparator"];
	    }
	}
	export class Options {
	    TitleBar?: TitleBar;
	    Appearance: string;
	    WebviewIsTransparent: boolean;
	    WindowIsTranslucent: boolean;
	    Preferences?: Preferences;
	    DisableZoom: boolean;
	    About?: AboutInfo;
	
	    static createFrom(source: any = {}) {
	        return new Options(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.TitleBar = this.convertValues(source["TitleBar"], TitleBar);
	        this.Appearance = source["Appearance"];
	        this.WebviewIsTransparent = source["WebviewIsTransparent"];
	        this.WindowIsTranslucent = source["WindowIsTranslucent"];
	        this.Preferences = this.convertValues(source["Preferences"], Preferences);
	        this.DisableZoom = source["DisableZoom"];
	        this.About = this.convertValues(source["About"], AboutInfo);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	

}

export namespace menu {
	
	export class MenuItem {
	    Label: string;
	    Role: number;
	    Accelerator?: keys.Accelerator;
	    Type: string;
	    Disabled: boolean;
	    Hidden: boolean;
	    Checked: boolean;
	    SubMenu?: Menu;
	
	    static createFrom(source: any = {}) {
	        return new MenuItem(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.Label = source["Label"];
	        this.Role = source["Role"];
	        this.Accelerator = this.convertValues(source["Accelerator"], keys.Accelerator);
	        this.Type = source["Type"];
	        this.Disabled = source["Disabled"];
	        this.Hidden = source["Hidden"];
	        this.Checked = source["Checked"];
	        this.SubMenu = this.convertValues(source["SubMenu"], Menu);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class Menu {
	    Items: MenuItem[];
	
	    static createFrom(source: any = {}) {
	        return new Menu(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.Items = this.convertValues(source["Items"], MenuItem);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}

}

export namespace options {
	
	export class DragAndDrop {
	    EnableFileDrop: boolean;
	    DisableWebViewDrop: boolean;
	    CSSDropProperty: string;
	    CSSDropValue: string;
	
	    static createFrom(source: any = {}) {
	        return new DragAndDrop(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.EnableFileDrop = source["EnableFileDrop"];
	        this.DisableWebViewDrop = source["DisableWebViewDrop"];
	        this.CSSDropProperty = source["CSSDropProperty"];
	        this.CSSDropValue = source["CSSDropValue"];
	    }
	}
	export class Debug {
	    OpenInspectorOnStartup: boolean;
	
	    static createFrom(source: any = {}) {
	        return new Debug(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.OpenInspectorOnStartup = source["OpenInspectorOnStartup"];
	    }
	}
	export class Experimental {
	
	
	    static createFrom(source: any = {}) {
	        return new Experimental(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	
	    }
	}
	export class SingleInstanceLock {
	    UniqueId: string;
	
	    static createFrom(source: any = {}) {
	        return new SingleInstanceLock(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.UniqueId = source["UniqueId"];
	    }
	}
	export class RGBA {
	    r: number;
	    g: number;
	    b: number;
	    a: number;
	
	    static createFrom(source: any = {}) {
	        return new RGBA(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.r = source["r"];
	        this.g = source["g"];
	        this.b = source["b"];
	        this.a = source["a"];
	    }
	}
	export class App {
	    Title: string;
	    Width: number;
	    Height: number;
	    DisableResize: boolean;
	    Fullscreen: boolean;
	    Frameless: boolean;
	    MinWidth: number;
	    MinHeight: number;
	    MaxWidth: number;
	    MaxHeight: number;
	    StartHidden: boolean;
	    HideWindowOnClose: boolean;
	    AlwaysOnTop: boolean;
	    BackgroundColour?: RGBA;
	    Assets: any;
	    AssetsHandler: any;
	    AssetServer?: assetserver.Options;
	    Menu?: menu.Menu;
	    LogLevel: number;
	    LogLevelProduction: number;
	    Bind: any[];
	    EnumBind: any[];
	    WindowStartState: number;
	    CSSDragProperty: string;
	    CSSDragValue: string;
	    EnableDefaultContextMenu: boolean;
	    EnableFraudulentWebsiteDetection: boolean;
	    SingleInstanceLock?: SingleInstanceLock;
	    Windows?: windows.Options;
	    Mac?: mac.Options;
	    Linux?: linux.Options;
	    // Go type: Experimental
	    Experimental?: any;
	    Debug: Debug;
	    DragAndDrop?: DragAndDrop;
	    DisablePanicRecovery: boolean;
	
	    static createFrom(source: any = {}) {
	        return new App(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.Title = source["Title"];
	        this.Width = source["Width"];
	        this.Height = source["Height"];
	        this.DisableResize = source["DisableResize"];
	        this.Fullscreen = source["Fullscreen"];
	        this.Frameless = source["Frameless"];
	        this.MinWidth = source["MinWidth"];
	        this.MinHeight = source["MinHeight"];
	        this.MaxWidth = source["MaxWidth"];
	        this.MaxHeight = source["MaxHeight"];
	        this.StartHidden = source["StartHidden"];
	        this.HideWindowOnClose = source["HideWindowOnClose"];
	        this.AlwaysOnTop = source["AlwaysOnTop"];
	        this.BackgroundColour = this.convertValues(source["BackgroundColour"], RGBA);
	        this.Assets = source["Assets"];
	        this.AssetsHandler = source["AssetsHandler"];
	        this.AssetServer = this.convertValues(source["AssetServer"], assetserver.Options);
	        this.Menu = this.convertValues(source["Menu"], menu.Menu);
	        this.LogLevel = source["LogLevel"];
	        this.LogLevelProduction = source["LogLevelProduction"];
	        this.Bind = source["Bind"];
	        this.EnumBind = source["EnumBind"];
	        this.WindowStartState = source["WindowStartState"];
	        this.CSSDragProperty = source["CSSDragProperty"];
	        this.CSSDragValue = source["CSSDragValue"];
	        this.EnableDefaultContextMenu = source["EnableDefaultContextMenu"];
	        this.EnableFraudulentWebsiteDetection = source["EnableFraudulentWebsiteDetection"];
	        this.SingleInstanceLock = this.convertValues(source["SingleInstanceLock"], SingleInstanceLock);
	        this.Windows = this.convertValues(source["Windows"], windows.Options);
	        this.Mac = this.convertValues(source["Mac"], mac.Options);
	        this.Linux = this.convertValues(source["Linux"], linux.Options);
	        this.Experimental = this.convertValues(source["Experimental"], null);
	        this.Debug = this.convertValues(source["Debug"], Debug);
	        this.DragAndDrop = this.convertValues(source["DragAndDrop"], DragAndDrop);
	        this.DisablePanicRecovery = source["DisablePanicRecovery"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	
	
	

}

export namespace windows {
	
	export class Messages {
	    InstallationRequired: string;
	    UpdateRequired: string;
	    MissingRequirements: string;
	    Webview2NotInstalled: string;
	    Error: string;
	    FailedToInstall: string;
	    DownloadPage: string;
	    PressOKToInstall: string;
	    ContactAdmin: string;
	    InvalidFixedWebview2: string;
	    WebView2ProcessCrash: string;
	
	    static createFrom(source: any = {}) {
	        return new Messages(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.InstallationRequired = source["InstallationRequired"];
	        this.UpdateRequired = source["UpdateRequired"];
	        this.MissingRequirements = source["MissingRequirements"];
	        this.Webview2NotInstalled = source["Webview2NotInstalled"];
	        this.Error = source["Error"];
	        this.FailedToInstall = source["FailedToInstall"];
	        this.DownloadPage = source["DownloadPage"];
	        this.PressOKToInstall = source["PressOKToInstall"];
	        this.ContactAdmin = source["ContactAdmin"];
	        this.InvalidFixedWebview2 = source["InvalidFixedWebview2"];
	        this.WebView2ProcessCrash = source["WebView2ProcessCrash"];
	    }
	}
	export class ThemeSettings {
	    DarkModeTitleBar: number;
	    DarkModeTitleBarInactive: number;
	    DarkModeTitleText: number;
	    DarkModeTitleTextInactive: number;
	    DarkModeBorder: number;
	    DarkModeBorderInactive: number;
	    LightModeTitleBar: number;
	    LightModeTitleBarInactive: number;
	    LightModeTitleText: number;
	    LightModeTitleTextInactive: number;
	    LightModeBorder: number;
	    LightModeBorderInactive: number;
	
	    static createFrom(source: any = {}) {
	        return new ThemeSettings(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.DarkModeTitleBar = source["DarkModeTitleBar"];
	        this.DarkModeTitleBarInactive = source["DarkModeTitleBarInactive"];
	        this.DarkModeTitleText = source["DarkModeTitleText"];
	        this.DarkModeTitleTextInactive = source["DarkModeTitleTextInactive"];
	        this.DarkModeBorder = source["DarkModeBorder"];
	        this.DarkModeBorderInactive = source["DarkModeBorderInactive"];
	        this.LightModeTitleBar = source["LightModeTitleBar"];
	        this.LightModeTitleBarInactive = source["LightModeTitleBarInactive"];
	        this.LightModeTitleText = source["LightModeTitleText"];
	        this.LightModeTitleTextInactive = source["LightModeTitleTextInactive"];
	        this.LightModeBorder = source["LightModeBorder"];
	        this.LightModeBorderInactive = source["LightModeBorderInactive"];
	    }
	}
	export class Options {
	    WebviewIsTransparent: boolean;
	    WindowIsTranslucent: boolean;
	    DisableWindowIcon: boolean;
	    IsZoomControlEnabled: boolean;
	    ZoomFactor: number;
	    DisablePinchZoom: boolean;
	    DisableFramelessWindowDecorations: boolean;
	    WebviewUserDataPath: string;
	    WebviewBrowserPath: string;
	    Theme: number;
	    CustomTheme?: ThemeSettings;
	    BackdropType: number;
	    Messages?: Messages;
	    ResizeDebounceMS: number;
	    WebviewGpuIsDisabled: boolean;
	    WebviewDisableRendererCodeIntegrity: boolean;
	    EnableSwipeGestures: boolean;
	    WindowClassName: string;
	
	    static createFrom(source: any = {}) {
	        return new Options(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.WebviewIsTransparent = source["WebviewIsTransparent"];
	        this.WindowIsTranslucent = source["WindowIsTranslucent"];
	        this.DisableWindowIcon = source["DisableWindowIcon"];
	        this.IsZoomControlEnabled = source["IsZoomControlEnabled"];
	        this.ZoomFactor = source["ZoomFactor"];
	        this.DisablePinchZoom = source["DisablePinchZoom"];
	        this.DisableFramelessWindowDecorations = source["DisableFramelessWindowDecorations"];
	        this.WebviewUserDataPath = source["WebviewUserDataPath"];
	        this.WebviewBrowserPath = source["WebviewBrowserPath"];
	        this.Theme = source["Theme"];
	        this.CustomTheme = this.convertValues(source["CustomTheme"], ThemeSettings);
	        this.BackdropType = source["BackdropType"];
	        this.Messages = this.convertValues(source["Messages"], Messages);
	        this.ResizeDebounceMS = source["ResizeDebounceMS"];
	        this.WebviewGpuIsDisabled = source["WebviewGpuIsDisabled"];
	        this.WebviewDisableRendererCodeIntegrity = source["WebviewDisableRendererCodeIntegrity"];
	        this.EnableSwipeGestures = source["EnableSwipeGestures"];
	        this.WindowClassName = source["WindowClassName"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}

}

