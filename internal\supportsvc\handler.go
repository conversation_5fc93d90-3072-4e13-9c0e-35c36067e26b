package supportsvc

// Để tiện mở rộng, file này chỉ giữ interface và helper chung.
// (<PERSON>ỳ nhu cầu có thể thêm logger, metrics, tracing…)

// Helper: publish error ra output (nếu bạn muốn chuẩn hoá thông điệp lỗi)
// Ở ví dụ này, phần publish nằm trong từng handler cụ thể để linh hoạt hơn.
type Publisher interface {
	Publish(topic string, qos byte, retained bool, payload interface{}) error
}

// Bạn có thể định nghĩa một publisher dùng trực tiếp mqtt.Client nếu muốn.
// <PERSON><PERSON> nhiê<PERSON>, trong ví dụ handler_database.go mình publish trực tiếp bằng mqtt.Client
// để giảm “bọc” trung gian.
