
go mod init example.com/hello-server
go run .
go build -o SupportSystem.exe ./cmd/server

go mod tidy



# 2) T<PERSON><PERSON> lib MQTT
go get github.com/eclipse/paho.mqtt.golang@v1   


.
├─ cmd/
│  └─ server/
│     └─ main.go
├─ internal/
│  ├─ config/
│  │  └─ config.go
│  ├─ httpserver/
│  │  └─ httpserver.go
│  ├─ mqttclient/
│  │  └─ mqttclient.go
│  ├─ sign/
│  │  └─ sign.go
│  └─ sysinfo/
│     └─ sysinfo.go
├─ config.json
└─ go.mod



NODE-RED SEND
msg.payload = {
    typeComment: "database",
    payload: {
        action: "query",
        org: "ReportSystem",
        bucket: "Dao1",
        query: `
            from(bucket: "Dao1")
            |> range(start: -100h)
            |> filter(fn: (r) => r._measurement == "Dao1_DongDien" and (r._field == "ampe1" or r._field == "ampe2"))
            |> group(columns: ["device"])
            |> pivot(rowKey: ["_time"], columnKey: ["_field"], valueColumn: "_value")
            |> keep(columns: ["_time","device","ampe1","ampe2"])
            |> sort(columns: ["_time"], desc: true)
            `.trim(),
        correlationId: "req-001",
        script: {
            "lang": "starlark",
            "code": `
def run(rows):
    out_date = []
    out_a1 = []
    out_a2 = []
    for r in rows:
        t = r.get("_time")
        d = time_format(t, tz="Asia/Ho_Chi_Minh", fmt="02-01-2006 15:04:05.000")
        out_date.append(d)
        out_a1.append(to_float(r.get("ampe1")))
        out_a2.append(to_float(r.get("ampe2")))
    return {"date": out_date, "data": out_a1, "ampe2": out_a2}`
        },
        output: "SystemSupport/Response",
        timeoutMs: 15000 
    }
};
return msg;