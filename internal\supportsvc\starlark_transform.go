package supportsvc

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"go.starlark.net/starlark"
)

// Không giới hạn: không đặt timeout (timeout<=0). Chỉ cancel nếu ctx.Done().
// Script có thể định nghĩa run(rows) để trả kết quả. Nếu không có run(),
// ta cố gắng eval code và lấy biến toàn cục tên RESULT (nếu muốn mở rộng).
func runStarlarkTransform(ctx context.Context, code string, rows []map[string]any, timeout time.Duration) (any, error) {
	thread := &starlark.Thread{Name: "db-script"}

	// Nếu timeout>0 thì dùng context.WithTimeout; nếu =0 không set deadline
	var cancel context.CancelFunc
	if timeout > 0 {
		ctx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	}

	done := make(chan struct{})
	var runErr error
	var result any

	go func() {
		defer close(done)

		pre := starlark.StringDict{
			// helpers hữu ích nhưng không bắt buộc
			"time_format":   starlark.NewBuiltin("time_format", bltnTimeFormat),
			"to_float":      starlark.NewBuiltin("to_float", bltnToFloat),
			"to_int":        starlark.NewBuiltin("to_int", bltnToInt),
			"to_str":        starlark.NewBuiltin("to_str", bltnToStr),
			"epoch_ms":      starlark.NewBuiltin("epoch_ms", bltnEpochMS),
			"parse_time":    starlark.NewBuiltin("parse_time", bltnParseTime),
			"from_epoch_ms": starlark.NewBuiltin("from_epoch_ms", bltnFromEpochMS),
			"now_ms":        starlark.NewBuiltin("now_ms", bltnNowMS),
		}

		globals, err := starlark.ExecFile(thread, "script.star", code, pre)
		if err != nil {
			runErr = fmt.Errorf("compile: %w", err)
			return
		}

		runVal, hasRun := globals["run"]
		if hasRun {
			argRows := goRowsToStarlark(rows)
			ret, err := starlark.Call(thread, runVal, starlark.Tuple{argRows}, nil)
			if err != nil {
				runErr = fmt.Errorf("runtime: %w", err)
				return
			}
			result = starlarkToGo(ret)
			return
		}

		// Không có run(): trả lại nguyên rows (không hạn chế)
		result = rows
	}()

	select {
	case <-ctx.Done():
		reason := "context canceled"
		if e := ctx.Err(); e != nil {
			reason = e.Error()
		}
		thread.Cancel(reason)
		<-done
		if runErr != nil {
			return nil, runErr
		}
		return nil, fmt.Errorf("script canceled: %s", reason)
	case <-done:
		return result, runErr
	}
}

// ---- Builtins & chuyển đổi (giữ nguyên như trước) ----

func bltnTimeFormat(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var v starlark.Value
	var zone, fmtStr string
	zone = "Asia/Ho_Chi_Minh"
	fmtStr = "02-01-2006 15:04:05.000"
	if err := starlark.UnpackArgs("time_format", args, kwargs, "value", &v, "tz?", &zone, "fmt?", &fmtStr); err != nil {
		return nil, err
	}
	ms, ok := toUnixMilliFromStarlark(v)
	if !ok {
		return starlark.None, nil
	}
	loc, err := time.LoadLocation(zone)
	if err != nil || loc == nil {
		loc = time.FixedZone(zone, 7*3600)
	}
	return starlark.String(time.UnixMilli(ms).In(loc).Format(fmtStr)), nil
}

func bltnToFloat(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var v starlark.Value
	if err := starlark.UnpackArgs("to_float", args, kwargs, "value", &v); err != nil {
		return nil, err
	}
	f, ok := toFloat64FromStarlark(v)
	if !ok || math.IsNaN(f) || math.IsInf(f, 0) {
		return starlark.None, nil
	}
	return starlark.Float(f), nil
}

func bltnToInt(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var v starlark.Value
	if err := starlark.UnpackArgs("to_int", args, kwargs, "value", &v); err != nil {
		return nil, err
	}
	f, ok := toFloat64FromStarlark(v)
	if !ok {
		return starlark.None, nil
	}
	return starlark.MakeInt(int(f)), nil
}

func bltnToStr(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var v starlark.Value
	if err := starlark.UnpackArgs("to_str", args, kwargs, "value", &v); err != nil {
		return nil, err
	}
	return starlark.String(v.String()), nil
}

func bltnEpochMS(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var v starlark.Value
	if err := starlark.UnpackArgs("epoch_ms", args, kwargs, "value", &v); err != nil {
		return nil, err
	}
	ms, ok := toUnixMilliFromStarlark(v)
	if !ok {
		return starlark.None, nil
	}
	return starlark.MakeInt64(ms), nil
}

func bltnParseTime(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var s, layout string
	if err := starlark.UnpackArgs("parse_time", args, kwargs, "s", &s, "layout?", &layout); err != nil {
		return nil, err
	}
	var t time.Time
	var err error
	if layout == "" {
		t, err = time.Parse(time.RFC3339Nano, s)
		if err != nil {
			t, err = time.Parse(time.RFC3339, s)
		}
	} else {
		t, err = time.Parse(layout, s)
	}
	if err != nil {
		return starlark.None, nil
	}
	return starlark.MakeInt64(t.UnixMilli()), nil
}

func bltnFromEpochMS(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var ms int64
	var zone string
	zone = "UTC"
	if err := starlark.UnpackArgs("from_epoch_ms", args, kwargs, "ms", &ms, "tz?", &zone); err != nil {
		return nil, err
	}
	loc, err := time.LoadLocation(zone)
	if err != nil || loc == nil {
		loc = time.UTC
	}
	return starlark.String(time.UnixMilli(ms).In(loc).Format(time.RFC3339Nano)), nil
}

func bltnNowMS(_ *starlark.Thread, _ *starlark.Builtin, _ starlark.Tuple, _ []starlark.Tuple) (starlark.Value, error) {
	return starlark.MakeInt64(time.Now().UnixMilli()), nil
}

// ---- Convert Go <-> Starlark ----

func goRowsToStarlark(rows []map[string]any) starlark.Value {
	lst := make([]starlark.Value, 0, len(rows))
	for _, r := range rows {
		d := starlark.NewDict(len(r))
		for k, v := range r {
			_ = d.SetKey(starlark.String(k), goToStarlark(v))
		}
		lst = append(lst, d)
	}
	return starlark.NewList(lst)
}

func goToStarlark(v any) starlark.Value {
	switch x := v.(type) {
	case nil:
		return starlark.None
	case bool:
		return starlark.Bool(x)
	case int:
		return starlark.MakeInt(x)
	case int64:
		return starlark.MakeInt64(x)
	case float32:
		return starlark.Float(float64(x))
	case float64:
		return starlark.Float(x)
	case string:
		return starlark.String(x)
	case time.Time:
		return starlark.String(x.Format(time.RFC3339Nano))
	case []any:
		arr := make([]starlark.Value, len(x))
		for i, e := range x {
			arr[i] = goToStarlark(e)
		}
		return starlark.NewList(arr)
	case map[string]any:
		d := starlark.NewDict(len(x))
		for k, v := range x {
			_ = d.SetKey(starlark.String(k), goToStarlark(v))
		}
		return d
	default:
		return starlark.String(fmt.Sprint(x))
	}
}

func starlarkToGo(v starlark.Value) any {
	switch x := v.(type) {
	case starlark.NoneType:
		return nil
	case starlark.Bool:
		return bool(x)
	case starlark.Int:
		i, _ := x.Int64()
		return i
	case starlark.Float:
		return float64(x)
	case starlark.String:
		return string(x)
	case *starlark.List:
		out := make([]any, x.Len())
		it := x.Iterate()
		defer it.Done()
		var item starlark.Value
		i := 0
		for it.Next(&item) {
			out[i] = starlarkToGo(item)
			i++
		}
		return out
	case *starlark.Dict:
		out := make(map[string]any, x.Len())
		for _, kv := range x.Items() {
			k := kv[0]
			v := kv[1]
			out[fmt.Sprint(k)] = starlarkToGo(v)
		}
		return out
	default:
		return x.String()
	}
}

func toUnixMilliFromStarlark(v starlark.Value) (int64, bool) {
	switch t := v.(type) {
	case starlark.Int:
		i, ok := t.Int64()
		if !ok {
			return 0, false
		}
		s := t.String()
		digits := len(strings.TrimLeft(s, "-+"))
		switch {
		case digits <= 11:
			return i * 1000, true
		case digits <= 13:
			return i, true
		case digits <= 16:
			return i / 1000, true
		default:
			return i / 1_000_000, true
		}
	case starlark.Float:
		return int64(float64(t)), true
	case starlark.String:
		str := string(t)
		if tm, err := time.Parse(time.RFC3339Nano, str); err == nil {
			return tm.UnixMilli(), true
		}
		if tm, err := time.Parse("2006-01-02 15:04:05", str); err == nil {
			return tm.UnixMilli(), true
		}
		if n, err := strconv.ParseInt(strings.TrimSpace(str), 10, 64); err == nil {
			return n, true
		}
		return 0, false
	default:
		return 0, false
	}
}

func toFloat64FromStarlark(v starlark.Value) (float64, bool) {
	switch t := v.(type) {
	case starlark.Float:
		return float64(t), true
	case starlark.Int:
		i, _ := t.Int64()
		return float64(i), true
	case starlark.String:
		f, err := strconv.ParseFloat(strings.TrimSpace(string(t)), 64)
		return f, err == nil
	default:
		return math.NaN(), false
	}
}
