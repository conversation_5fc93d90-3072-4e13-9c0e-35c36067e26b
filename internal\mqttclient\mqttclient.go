package mqttclient

import (
	"context"
	"crypto/tls"
	"fmt"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"

	"Golang/SystemSupport/internal/config"

	kvstore "Golang/SystemSupport/internal/store"

	wruntime "github.com/wailsapp/wails/v2/pkg/runtime"
)

type Handle struct {
	client mqtt.Client
}

// Client exposes the underlying mqtt.Client (read-only).
func (h *Handle) Client() mqtt.Client {
	return h.client
}

func (h *Handle) Close() {
	h.CloseWithOffline("SystemSupportStatus", "offline", 1, true)
}

func (h *Handle) CloseWithOffline(topic, offline string, qos byte, retain bool) {
	if h.client == nil || !h.client.IsConnectionOpen() {
		return
	}
	// Chủ động đặt OFFLINE (retained) trước khi ngắt — Will sẽ KHÔNG bắn khi disconnect “đẹp”
	tok := h.client.Publish(topic, qos, retain, offline)
	_ = tok.WaitTimeout(2 * time.Second) // best-effort
	h.client.Disconnect(250)
}

// Start khởi tạo MQTT client từ config. Nếu cfg == nil: trả nil, nil.
func Start(ctx context.Context, cfg *config.MQTT) (*Handle, error) {
	if cfg == nil || cfg.Host == "" || cfg.Port == 0 {
		return nil, nil
	}

	// Mặc định
	statusTopic := cfg.StatusTopic
	if statusTopic == "" {
		statusTopic = "SystemSupportStatus"
	}
	onlineMsg := cfg.StatusOnline
	if onlineMsg == "" {
		onlineMsg = "online"
	}
	offlineMsg := cfg.StatusOffline
	if offlineMsg == "" {
		offlineMsg = "offline"
	}
	retain := cfg.StatusRetain
	if !cfg.StatusRetain {
		retain = true
	} // default true
	qos := cfg.StatusQOS
	if qos == 0 {
		qos = 0
	} // default QoS 1

	scheme := "tcp"
	var tlsCfg *tls.Config
	if cfg.TLS.Enable {
		scheme = "ssl"
		tlsCfg = &tls.Config{InsecureSkipVerify: cfg.TLS.InsecureSkipVerify}
	}

	brokerURL := fmt.Sprintf("%s://%s:%d", scheme, cfg.Host, cfg.Port)

	opts := mqtt.NewClientOptions().AddBroker(brokerURL)
	clientID := cfg.ClientID
	if clientID == "" {
		clientID = fmt.Sprintf("sysinfo-%d", time.Now().UnixNano())
	}
	opts.SetClientID(clientID)
	if cfg.Username != "" {
		opts.SetUsername(cfg.Username)
		opts.SetPassword(cfg.Password)
	}
	if tlsCfg != nil {
		opts.SetTLSConfig(tlsCfg)
	}
	opts.SetCleanSession(false) // GIỮ session & subscriptions qua reconnect cho supportsvc Topic
	opts.SetAutoReconnect(true)
	opts.SetConnectTimeout(5 * time.Second)
	opts.SetKeepAlive(30 * time.Second)
	opts.SetOrderMatters(false)

	// 🔹 Will: broker sẽ tự publish OFFLINE khi client “chết”
	opts.SetWill(statusTopic, offlineMsg, qos, retain)

	topic := cfg.TopicSupport
	if topic == "" {
		topic = "SystemSupport"
	}

	opts.OnConnect = func(c mqtt.Client) {
		wruntime.EventsEmit(ctx, "mqttlog", "MQTT CONNECTED")
		var temp = "MQTT connected -> " + brokerURL + " (client_id=" + clientID + ")"
		kvstore.QAdd("MQTT_LogBuffer", temp)

		// 🔹 Birth: publish ONLINE (retained) khi vừa kết nối
		tok := c.Publish(statusTopic, qos, retain, onlineMsg)
		if ok := tok.WaitTimeout(5 * time.Second); !ok {
			// log.Printf("MQTT publish timeout on %q", statusTopic)
			kvstore.QAdd("MQTT_LogBuffer", "MQTT publish timeout on "+statusTopic)
		} else if err := tok.Error(); err != nil {
			// log.Printf("MQTT publish error: %v", err)
			kvstore.QAdd("MQTT_LogBuffer", "MQTT publish error: "+err.Error())
		} else {
			// log.Printf("MQTT status %q -> %q (retained=%v, qos=%d)", statusTopic, onlineMsg, retain, qos)
			kvstore.QAdd("MQTT_LogBuffer", "MQTT status "+statusTopic+" -> "+onlineMsg+" (retained="+fmt.Sprintf("%v", retain)+", qos="+fmt.Sprintf("%d", qos)+")")
		}

	}

	opts.OnConnectionLost = func(c mqtt.Client, err error) {
		// log.Printf("MQTT connection lost: %v", err)
		kvstore.QAdd("MQTT_LogBuffer", "MQTT connection lost: "+err.Error())
	}

	client := mqtt.NewClient(opts)
	tok := client.Connect()
	if ok := tok.WaitTimeout(10 * time.Second); !ok {
		kvstore.QAdd("MQTT_LogBuffer", "MQTT connect timeout to "+brokerURL)
		return nil, fmt.Errorf("mqtt connect timeout to %s", brokerURL)
	}
	if err := tok.Error(); err != nil {
		kvstore.QAdd("MQTT_LogBuffer", "MQTT connect error: "+err.Error())
		return nil, fmt.Errorf("mqtt connect error: %w", err)
	}

	h := &Handle{client: client}

	// đóng khi ctx hủy
	go func() {
		<-ctx.Done()
		h.CloseWithOffline(statusTopic, offlineMsg, qos, retain)
	}()

	return h, nil
}
