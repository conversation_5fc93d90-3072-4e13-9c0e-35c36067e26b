{"listen_addr": "localhost:8443", "require_client_cert": true, "certs": {"host_cert": "certs\\host.crt", "host_key": "certs\\host.key", "ca_cert": "certs\\ca.crt"}, "hmac": {"secret": "mysecret123", "secret_file": "certs\\hmac_secret.txt"}, "MQTT": {"Host": "localhost", "Port": 1883, "ClientID": "go-systemsupport", "Username": "admin", "Password": "123", "TopicSupport": "SystemSupport", "StatusTopic": "SystemSupportStatus", "StatusTopicDatabaseRead": "SystemSupportStatusDatabaseRead", "StatusTopicDatabaseWrite": "SystemSupportStatusDatabaseWrite", "TLS": {"Enable": false, "InsecureSkipVerify": false}}, "Influx": {"Enable": true, "Url": "http://localhost:8086", "Token": "yWZo5tXbdjsTp5vbEGoUFCuJ-xvDGByL30cdnDpqiV56GvVwi_pCbmlbkB-wijZKlMM2aly5ZQ_U4mOCgVTrQA==", "Organization": "ReportSystem", "DefaultBucket": "Dao1"}}