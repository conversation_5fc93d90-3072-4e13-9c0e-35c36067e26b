package supportsvc

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// Querier: trừu tượng hoá lớp truy vấn Influx (đã có adapter ở main)
type Querier interface {
	Query(ctx context.Context, org, bucket, flux string) ([]map[string]any, error)
	Close() error
}

type DatabaseHandler struct {
	MQTT          mqtt.Client
	QoS           byte
	Retain        bool
	DefaultOrg    string
	DefaultBucket string
	Querier       Querier

	// Không giới hạn: để 0 => không timeout, không cắt rows
	ScriptTimeout time.Duration
	MaxRows       int

	Writer LineWriter // <--- THÊM (để ghi)
}

func (h *DatabaseHandler) Kind() string { return "database" }

type DBPayload struct {
	Action        string      `json:"action"`
	Org           string      `json:"org,omitempty"`
	Bucket        string      `json:"bucket,omitempty"`
	Query         string      `json:"query,omitempty"`
	Script        *ScriptSpec `json:"script,omitempty"`
	ResponseTopic string      `json:"responseTopic,omitempty"`
	CorrelationID string      `json:"correlationId,omitempty"`
	Output        string      `json:"output,omitempty"` // back-compat

	// ---- write ----
	Precision    string `json:"precision,omitempty"`    // "ns","us","ms","s" (mặc định "ns" nếu bỏ)
	LineProtocol string `json:"lineProtocol,omitempty"` // 1 hoặc nhiều dòng
}

type ScriptSpec struct {
	Lang string `json:"lang"` // "starlark"
	Code string `json:"code"` // phải định nghĩa run(rows) hoặc có thể trả trực tiếp
}

func (h *DatabaseHandler) Handle(ctx context.Context, raw json.RawMessage) error {
	var p DBPayload
	if err := json.Unmarshal(raw, &p); err != nil {
		return fmt.Errorf("database payload invalid: %w", err)
	}
	if p.Action == "" {
		p.Action = "query"
	}
	if !strings.EqualFold(p.Action, "query") && !strings.EqualFold(p.Action, "write") {
		return fmt.Errorf("unsupported action: %s", p.Action)
	}

	if strings.EqualFold(p.Action, "write") {
		org := p.Org
		if org == "" {
			org = h.DefaultOrg
		}
		bucket := p.Bucket
		if bucket == "" {
			bucket = h.DefaultBucket
		}
		if org == "" || bucket == "" {
			return h.publishError(p, fmt.Errorf("org/bucket required for write"))
		}
		if h.Writer == nil {
			return h.publishError(p, fmt.Errorf("writer not configured"))
		}
		prec := p.Precision
		if prec == "" {
			prec = "ns"
		} // Influx default
		lp := strings.TrimSpace(p.LineProtocol)
		if lp == "" {
			return h.publishError(p, fmt.Errorf("lineProtocol required"))
		}
		// Đảm bảo có newline cuối (influx accept nhiều dòng)
		if !strings.HasSuffix(lp, "\n") {
			lp += "\n"
		}
		if err := h.Writer.WriteLineProtocol(ctx, org, bucket, prec, []byte(lp)); err != nil {
			return h.publishError(p, fmt.Errorf("write error: %w", err))
		}
		// OK
		ack := map[string]any{
			"ok":            true,
			"correlationId": p.CorrelationID,
			"result":        "written",
		}
		buf, _ := json.Marshal(ack)
		topic := p.ResponseTopic
		if topic == "" {
			topic = p.Output
			if topic == "" {
				topic = "SystemSupport/Response"
			}
		}
		tok := h.MQTT.Publish(topic, h.QoS, h.Retain, buf)
		tok.Wait()
		return tok.Error()
	}

	org := p.Org
	if org == "" {
		org = h.DefaultOrg
	}
	bucket := p.Bucket
	if bucket == "" {
		bucket = h.DefaultBucket
	}
	start := time.Now()
	if strings.TrimSpace(p.Query) == "" {
		return errors.New("missing query")
	}

	rows, err := h.Querier.Query(ctx, org, bucket, p.Query)
	dur := time.Since(start)
	if err != nil {
		return h.publishError(p, fmt.Errorf("query error: %w", err))
	}

	// Không giới hạn: không cắt rows (trừ khi MaxRows>0 được set thủ công)
	if h.MaxRows > 0 && len(rows) > h.MaxRows {
		rows = rows[:h.MaxRows]
	}

	var result any = rows
	if p.Script != nil {
		lang := strings.ToLower(strings.TrimSpace(p.Script.Lang))
		switch lang {
		case "starlark":
			timeout := h.ScriptTimeout
			if timeout <= 0 {
				timeout = 2 * time.Second
			}
			res, sErr := runStarlarkTransform(ctx, p.Script.Code, rows, timeout)
			if sErr != nil {
				return h.publishError(p, fmt.Errorf("script error: %w", sErr))
			}
			result = res
		case "go":
			// có thể để 0 = không timeout, hoặc dùng h.ScriptTimeout / payload riêng nếu muốn
			res, sErr := runGoScript(ctx, p.Script.Code, rows, h.ScriptTimeout)
			if sErr != nil {
				return h.publishError(p, fmt.Errorf("script error: %w", sErr))
			}
			result = res
		default:
			return h.publishError(p, fmt.Errorf("unsupported script.lang: %s", p.Script.Lang))
		}
	}

	resp := map[string]any{"ok": true, "correlationId": p.CorrelationID, "result": result, "durationMs": dur.Milliseconds()}
	buf, _ := json.Marshal(resp)
	topic := p.ResponseTopic
	if topic == "" {
		topic = p.Output
	}
	if topic == "" {
		topic = "SystemSupport/Response"
	}
	tok := h.MQTT.Publish(topic, h.QoS, h.Retain, buf)
	tok.Wait()
	if err := tok.Error(); err != nil {
		log.Printf("[DatabaseHandler] publish error: %v", err)
		return err
	}
	return nil
}

func (h *DatabaseHandler) publishError(p DBPayload, err error) error {
	resp := map[string]any{"ok": false, "correlationId": p.CorrelationID, "error": err.Error()}
	buf, _ := json.Marshal(resp)
	topic := p.ResponseTopic
	if topic == "" {
		topic = p.Output
	}
	if topic == "" {
		topic = "SystemSupport/Response"
	}
	tok := h.MQTT.Publish(topic, h.QoS, h.Retain, buf)
	tok.Wait()
	return err
}
