package uiapp

import (
	"context"
	"encoding/json"
	"fmt"
	"sync/atomic"
	"time"

	"Golang/SystemSupport/internal/config"

	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// App: đối tượng bind sang UI (được Wails tạo wailsjs để gọi từ React)
type App struct {
	ctx context.Context
}

func New() *App { return &App{} }

// Startup được Wails gọi 1 lần khi app khởi động
func (a *App) Startup(ctx context.Context) {
	a.ctx = ctx
	globalCtx.Store(ctx) // lưu để tiện Emit từ package khác

	// Emit MQTT info sau khi startup
	go func() {
		time.Sleep(2 * time.Second) // Đợi UI sẵn sàng
		mqttInfoJson, influxInfoJson := a.GetConfigInfo()
		runtime.EventsEmit(ctx, "mqttinfo", mqttInfoJson)
		fmt.Printf("config: %s\n", string(influxInfoJson))
		runtime.EventsEmit(ctx, "influxinfo", influxInfoJson)
	}()
}

// Hàm public để UI gọi (hoặc backend khác gọi thông qua binding)
func (a *App) PushStatus(s string) {
	runtime.EventsEmit(a.ctx, "status", s)
}

// Test function để emit serverlog từ UI
func (a *App) TestServerLog(message string) {
	runtime.EventsEmit(a.ctx, "serverlog", message)
}

// TestMQTT: test method đơn giản
func (a *App) TestMQTT() string {
	return "MQTT test OK"
}

// MQTTInfo: struct chứa thông tin MQTT để trả về UI
type MQTTInfo struct {
	Host     string `json:"Host"`
	Port     int    `json:"Port"`
	ClientID string `json:"ClientID"`
	Status   string `json:"Status"`
}
type InfluxInfo struct {
	Url          string
	Organization string
	Status       string
}

// GetConfigInfo: lấy thông tin MQTT từ config để hiển thị trên UI
func (a *App) GetConfigInfo() (string, string) {
	// Load config từ file, nếu không có thì trả về mặc định
	if cfg, err := config.Load("config.json"); err == nil && cfg != nil {
		var info MQTTInfo
		if cfg.MQTT == nil {
			info = MQTTInfo{
				Host:     "localhost",
				Port:     1883,
				ClientID: "go-systemsupport",
				Status:   "No config",
			}
		} else {
			info = MQTTInfo{
				Host:     cfg.MQTT.Host,
				Port:     cfg.MQTT.Port,
				ClientID: cfg.MQTT.ClientID,
				Status:   "Configured",
			}
		}
		jsonData, _ := json.Marshal(info)

		var infoInflux InfluxInfo
		if cfg.Influx == nil {
			infoInflux = InfluxInfo{
				Url:          "---",
				Organization: "No config",
				Status:       "No config",
			}
		} else {
			infoInflux = InfluxInfo{
				Url:          cfg.Influx.Url,
				Organization: cfg.Influx.Organization,
				Status:       "Configured",
			}
		}
		jsonDataInflux, _ := json.Marshal(infoInflux)

		return string(jsonData), string(jsonDataInflux)
	} else {
		info := MQTTInfo{
			Host:     "localhost",
			Port:     1883,
			ClientID: "go-systemsupport",
			Status:   "Config error",
		}
		jsonData, _ := json.Marshal(info)

		infoInflux := InfluxInfo{
			Url:          "---",
			Organization: "---",
			Status:       "Config error",
		}
		jsonDataInflux, _ := json.Marshal(infoInflux)
		return string(jsonData), string(jsonDataInflux)
	}
}

// Trả về options cho wails.Run
func (a *App) Options() *options.App {
	return &options.App{
		Title:     "SystemSupport",
		OnStartup: a.Startup,
		Bind:      []interface{}{a},
	}
}

// --------- Helpers để emit event từ mọi nơi trong code base ---------

var globalCtx atomic.Value // holds context.Context

// EmitStatus: có thể gọi từ bất kỳ package nào sau khi app đã Startup
func EmitStatus(s string) {
	if v := globalCtx.Load(); v != nil {
		runtime.EventsEmit(v.(context.Context), "status", s)
	}
}
