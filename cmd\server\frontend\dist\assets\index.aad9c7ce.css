html{background-color:#1b2636;text-align:center;color:#fff}body{margin:0;color:#fff;font-family:Nunito,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif}@font-face{font-family:Nunito;font-style:normal;font-weight:400;src:local(""),url(/assets/nunito-v16-latin-regular.06f3af3f.woff2) format("woff2")}#app{height:100vh;text-align:center}.wrap{min-height:100dvh;display:grid;place-items:center;background:radial-gradient(60% 50% at 70% 30%,rgba(0,120,255,.12),transparent 60%),radial-gradient(40% 35% at 20% 80%,rgba(0,200,255,.08),transparent 60%),#070d1a;padding:10px}.card{width:min(780px,92vw);border-radius:14px;padding:28px 28px 30px;color:#e6f0ff;background:linear-gradient(180deg,#0b1b33 0%,#071427 100%);position:relative;border:1px solid rgba(56,189,248,.25);box-shadow:0 10px 28px #0009,0 2px #0006 inset,0 0 0 1px #38bdf826 inset,0 0 24px #38bdf82e;transition:transform .3s ease,box-shadow .3s ease,border-color .3s ease}.card:before{content:"";position:absolute;inset:0;border-radius:14px;padding:1px;background:linear-gradient(120deg,rgba(56,189,248,.8),rgba(59,130,246,.35),rgba(56,189,248,.6));-webkit-mask:linear-gradient(#000 0 0) content-box,linear-gradient(#000 0 0);-webkit-mask-composite:xor;mask-composite:exclude;pointer-events:none;opacity:.55}.card:hover{transform:scale(1.03);box-shadow:0 18px 36px #000000b3,0 2px #00000073 inset,0 0 0 1px #38bdf838 inset,0 0 34px #38bdf838;border-color:#38bdf859}.card h1{margin:0 0 10px;font-size:clamp(20px,3.6vw,28px);letter-spacing:.2px;text-shadow:0 1px 0 rgba(0,0,0,.35)}.card p{margin:0;line-height:1.6;color:#c9e4ff;opacity:.95}
