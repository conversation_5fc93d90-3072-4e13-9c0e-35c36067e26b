html {
    background-color: rgba(27, 38, 54, 1);
    text-align: center;
    color: white;
}

body {
    margin: 0;
    color: white;
    font-family: "Nunito", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
        "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
        sans-serif;
}

@font-face {
    font-family: "Nunito";
    font-style: normal;
    font-weight: 400;
    src: local(""),
        url("assets/fonts/nunito-v16-latin-regular.woff2") format("woff2");
}

#app {
    height: 100vh;
    text-align: center;
}


.wrap {
    min-height: 100dvh;
    display: grid;
    place-items: center;
    background:
        radial-gradient(60% 50% at 70% 30%, rgba(0, 120, 255, 0.12), transparent 60%),
        radial-gradient(40% 35% at 20% 80%, rgba(0, 200, 255, 0.08), transparent 60%),
        #070d1a;
    padding: 1px;
}

.card {
    width: min(700px, 92vw);
    border-radius: 14px;
    padding: 15px;
    margin:5px;
    color: #e6f0ff;
    background: linear-gradient(180deg, #0b1b33 0%, #071427 100%);
    position: relative;

    border: 1px solid rgba(56, 189, 248, 0.25);
    box-shadow:
        0 10px 28px rgba(0, 0, 0, 0.6),
        0 2px 0 rgba(0, 0, 0, 0.4) inset,
        0 0 0 1px rgba(56, 189, 248, 0.15) inset,
        0 0 24px rgba(56, 189, 248, 0.18);

    transition: transform 300ms ease, box-shadow 300ms ease, border-color 300ms ease;
}

.card::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 14px;
    padding: 1px;
    background: linear-gradient(120deg,
            rgba(56, 189, 248, 0.8),
            rgba(59, 130, 246, 0.35),
            rgba(56, 189, 248, 0.6));
    -webkit-mask:
        linear-gradient(#000 0 0) content-box,
        linear-gradient(#000 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
    opacity: 0.55;
}

.card:hover {
    transform: scale(1.01);
    /* chỉ phóng to nhẹ */
    box-shadow:
        0 18px 36px rgba(0, 0, 0, 0.7),
        0 2px 0 rgba(0, 0, 0, 0.45) inset,
        0 0 0 1px rgba(56, 189, 248, 0.22) inset,
        0 0 34px rgba(56, 189, 248, 0.22);
    border-color: rgba(56, 189, 248, 0.35);
}

.card h1 {
    margin: 0 0 10px;
    font-size: clamp(20px, 3.6vw, 28px);
    letter-spacing: 0.2px;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.35);
}

.card p {
    margin: 0;
    line-height: 1.6;
    color: #c9e4ff;
    opacity: 0.95;
}