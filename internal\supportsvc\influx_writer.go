package supportsvc

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
)

type LineWriter interface {
	WriteLineProtocol(ctx context.Context, org, bucket, precision string, data []byte) error
}

// HTTPLineWriter: ghi trực tiếp qua /api/v2/write
type HTTPLineWriter struct {
	URL   string // ví dụ: http://localhost:8086
	Token string // InfluxDB v2 token
	// (tuỳ chọn) dùng http.Client riêng nếu cần
	Client *http.Client
}

func (w *HTTPLineWriter) WriteLineProtocol(ctx context.Context, org, bucket, precision string, data []byte) error {
	if w.Client == nil {
		w.Client = http.DefaultClient
	}
	base := strings.TrimRight(w.URL, "/")
	u := fmt.Sprintf("%s/api/v2/write?org=%s&bucket=%s&precision=%s",
		base,
		url.QueryEscape(org),
		url.QueryEscape(bucket),
		url.QueryEscape(precision),
	)
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, u, bytes.NewReader(data))
	if err != nil {
		return err
	}
	req.Header.Set("Authorization", "Token "+w.Token)
	req.Header.Set("Content-Type", "text/plain; charset=utf-8")
	resp, err := w.Client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode/100 != 2 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("influx write failed: %d %s", resp.StatusCode, string(body))
	}
	return nil
}
