package influx

import "context"

// Querier là interface chung để bạn có thể hoán đổi giữa InfluxDB 1.x / 2.x,
// hoặc giả lập trong unit test.
type Querier interface {
	// Query chạy Flux (InfluxDB 2.x). Trả về từng record dưới dạng map[string]interface{}
	// đã “phẳng hoá” (bao gồm các cột và _value).
	Query(ctx context.Context, org string, flux string) ([]map[string]interface{}, error)
	Close()
}
