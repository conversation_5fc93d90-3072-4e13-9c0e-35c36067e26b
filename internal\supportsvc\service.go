package supportsvc

import (
	"context"
	"encoding/json"
	"log"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// MessageEnvelope là “khung” chung cho các yêu cầu
type MessageEnvelope struct {
	TypeComment string          `json:"typeComment"` // ví dụ: "database"
	Payload     json.RawMessage `json:"payload"`
}

// Config điều chỉnh service
type Config struct {
	TopicSupport string // topic subscribe để nhận job (mặc định "SystemSupport")
	QoS          byte   // QoS subscribe (mặc định 1)
	Concurrency  int    // số worker (mặc định: runtime.NumCPU())
	// PublishRetain tuỳ use case, thường là false cho output data
	PublishRetain bool // mặc định false
}

type Handler interface {
	// Handle thực thi logic theo typeComment cụ thể.
	// rawPayload là phần "payload" trong envelope.
	Handle(ctx context.Context, rawPayload json.RawMessage) error
	// Kind trả về typeComment mà handler nhận.
	Kind() string
}

type Service struct {
	mqtt     mqtt.Client
	cfg      Config
	handlers map[string]Handler

	jobs    chan []byte
	wg      sync.WaitGroup
	started int32 // 0: chưa start, 1: đã start
}

func New(mqttClient mqtt.Client, cfg Config, hs ...Handler) *Service {
	if cfg.TopicSupport == "" {
		cfg.TopicSupport = "SystemSupport"
	}
	if cfg.QoS == 0 {
		cfg.QoS = 0
	}
	if cfg.Concurrency <= 0 {
		cfg.Concurrency = runtime.NumCPU()
	}
	hmap := make(map[string]Handler, len(hs))
	for _, h := range hs {
		hmap[h.Kind()] = h
	}
	return &Service{
		mqtt:     mqttClient,
		cfg:      cfg,
		handlers: hmap,
		jobs:     make(chan []byte, cfg.Concurrency*4),
	}
}

func (s *Service) Start(ctx context.Context) error {
	if !atomic.CompareAndSwapInt32(&s.started, 0, 1) {
		log.Printf("[supportsvc] Start() called but service already started")
		return nil // hoặc return fmt.Errorf("already started")
	}

	// 1) subscribe vào TopicSupport
	tok := s.mqtt.Subscribe(s.cfg.TopicSupport, s.cfg.QoS, s.onMessage)
	if ok := tok.WaitTimeout(5 * time.Second); !ok {
		return tok.Error()
	}
	if err := tok.Error(); err != nil {
		return err
	}
	log.Printf("MQTT: Subscribed %q (qos=%d, workers=%d)",
		s.cfg.TopicSupport, s.cfg.QoS, s.cfg.Concurrency)

	// 2) start worker-pool
	for i := 0; i < s.cfg.Concurrency; i++ {
		s.wg.Add(1)
		go s.worker(ctx, i)
	}

	// 3) chờ ctx hủy -> dọn dẹp
	go func() {
		<-ctx.Done()
		_ = s.mqtt.Unsubscribe(s.cfg.TopicSupport)
		close(s.jobs)
	}()

	return nil
}

func (s *Service) onMessage(_ mqtt.Client, msg mqtt.Message) {
	// log.Printf("[supportsvc] received on %q: %s", msg.Topic(), string(msg.Payload()))
	// Sao chép payload vì paho có thể tái sử dụng buffer sau callback
	raw := append([]byte(nil), msg.Payload()...)
	select {
	case s.jobs <- raw:
	default:
		// Hàng đợi đầy -> tránh block callback, ghi log drop
		log.Printf("MQTT: [supportsvc] job queue full, drop message on %q", msg.Topic())
	}
}

func (s *Service) worker(ctx context.Context, idx int) {
	defer s.wg.Done()
	for {
		select {
		case <-ctx.Done():
			return
		case raw, ok := <-s.jobs:
			if !ok {
				return
			}
			var env MessageEnvelope
			if err := json.Unmarshal(raw, &env); err != nil {
				log.Printf("MQTT: [supportsvc] worker#%d invalid json: %v", idx, err)
				continue
			}
			h, ok := s.handlers[env.TypeComment]
			if !ok {
				log.Printf("MQTT: [supportsvc] worker#%d no handler for type=%q", idx, env.TypeComment)
				continue
			}
			if err := h.Handle(ctx, env.Payload); err != nil {
				log.Printf("MQTT: [supportsvc] worker#%d handle(%s) error: %v", idx, env.TypeComment, err)
			}
		}
	}
}

// Wait cho phép main chờ worker kết thúc nếu cần.
func (s *Service) Wait() { s.wg.Wait() }
