// src/App.tsx (React)
import { useEffect, useState, useRef } from "react";
import { EventsOn, EventsEmit } from "../wailsjs/runtime/runtime";

// Type cho MQTT info
interface MQTTInfo {
  host: string;
  port: number;
  client_id: string;
  status: string;
}
interface InfluxInfo {
  Url: string;
  Organization: string;
  status: string;
}

export default function App() {
  const [status, setStatus] = useState("Starting...");
  const [logs, setLogs] = useState<string[]>([]);
  const [mqttLogs, setMqttLogs] = useState<string[]>([]);
  const [influxLogs, setInfluxLogs] = useState<string[]>([]);

  const [searchTerm, setSearchTerm] = useState("");
  const [mqttSearchTerm, setMqttSearchTerm] = useState("");
  const [influxSearchTerm, setInfluxSearchTerm] = useState("");

  const [autoScroll, setAutoScroll] = useState(true);
  const [mqttAutoScroll, setMqttAutoScroll] = useState(true);
  const [influxAutoScroll, setInfluxAutoScroll] = useState(true);

  const [mqttInfo, setMqttInfo] = useState<MQTTInfo | null>(null);
  const [influxInfo, setInfluxInfo] = useState<InfluxInfo | null>(null);

  const [mqttError, setMqttError] = useState<string>("");
  const [influxError, setInfluxError] = useState<string>("");

  const taRef = useRef<HTMLTextAreaElement>(null);
  const mqttTaRef = useRef<HTMLTextAreaElement>(null);
  const influxTaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    const off = EventsOn("status", (s: string) => setStatus(s));
    return () => off();
  }, []);

  // Listen for MQTT info event
  useEffect(() => {
    const off = EventsOn("mqttinfo", (jsonStr: string) => {
      try {
        const info = JSON.parse(jsonStr) as MQTTInfo;
        setMqttInfo(info);
        setMqttError("");
      } catch (error) {
        setMqttError(`Failed to parse MQTT info: ${error}`);
        console.error("Error parsing MQTT info:", error);
      }
    });
    return () => off();
  }, []);
  // Listen for Influx info event
  useEffect(() => {
    const off = EventsOn("influxinfo", (jsonStr: string) => {
      try {
        alert(jsonStr);
        const info = JSON.parse(jsonStr) as InfluxInfo;
        setInfluxInfo(info);
        setInfluxError("");
      } catch (error) {
        setInfluxError(`Failed to parse Influx info: ${error}`);
        console.error("Error parsing Influx info:", error);
      }
    });
    return () => off();
  }, []);


  // Listen for MQTT logs event
  useEffect(() => {
    const offMqttLog = EventsOn("mqttlog", (line: string) => {
      setMqttLogs(prev => {
        const next = [...prev, line];
        // Giới hạn tối đa 1000 dòng cho MQTT logs
        if (next.length > 1000) next.shift();
        return next;
      });
    });

    return () => {
      offMqttLog();
    };
  }, []);

  // Listen for Influx logs event
  useEffect(() => {
    const offInfluxLog = EventsOn("influxlog", (line: string) => {
      setInfluxLogs(prev => {
        const next = [...prev, line];
        // Giới hạn tối đa 1000 dòng cho Influx logs
        if (next.length > 1000) next.shift();
        return next;
      });
    });

    return () => {
      offInfluxLog();
    };
  }, []);



  // Nhận log dòng (từ Go -> "serverlog")
  useEffect(() => {
    const offLog = EventsOn("serverlog", (line: string) => {
      console.log("Received serverlog:", line);
      setLogs(prev => {
        const next = [...prev, line];
        // Giới hạn tối đa 5000 dòng để không phình RAM (tùy bạn)
        if (next.length > 5000) next.shift();
        console.log("Updated logs count:", next.length);
        return next;
      });
    });

    return () => {
      offLog();
    };
  }, []);

  // Auto scroll xuống cuối mỗi khi logs thay đổi (nếu enabled)
  useEffect(() => {
    if (!autoScroll) return;
    const ta = taRef.current;
    if (!ta) return;
    ta.scrollTop = ta.scrollHeight;
  }, [logs, autoScroll]);

  // Auto scroll cho MQTT logs
  useEffect(() => {
    if (!mqttAutoScroll) return;
    const ta = mqttTaRef.current;
    if (ta) {
      ta.scrollTop = ta.scrollHeight;
    }
  }, [mqttLogs, mqttAutoScroll]);

  // Auto scroll cho Influx logs
  useEffect(() => {
    if (!influxAutoScroll) return;
    const ta = influxTaRef.current;
    if (ta) {
      ta.scrollTop = ta.scrollHeight;
    }
  }, [influxLogs, influxAutoScroll]);

  // Filter logs based on search term
  const filteredLogs = searchTerm ?
    logs.filter(log => log.toLowerCase().includes(searchTerm.toLowerCase())) :
    logs;

  // Filter MQTT logs based on search term
  const filteredMqttLogs = mqttSearchTerm ?
    mqttLogs.filter(log => log.toLowerCase().includes(mqttSearchTerm.toLowerCase())) :
    mqttLogs;

  // Filter Influx logs based on search term
  const filteredInfluxLogs = influxSearchTerm ?
    influxLogs.filter(log => log.toLowerCase().includes(influxSearchTerm.toLowerCase())) :
    influxLogs;

  const clearLogs = () => setLogs([]);
  const clearMqttLogs = () => setMqttLogs([]);
  const clearInfluxLogs = () => setInfluxLogs([]);

  return (
    <div style={{ fontFamily: "sans-serif" }}>
      <main className="wrap">
        <div className="card">
          {/* MQTT Info Section */}
          <div style={{
            marginBottom: 12,
            padding: 8,
            backgroundColor: "#f5f5f5",
            borderRadius: 4,
            border: "1px solid #ddd",
            color: 'black'
          }}>
            <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>MQTT Broker Info</h4>
            {mqttError ? (
              <div style={{ color: "red", fontSize: "12px" }}>{mqttError}</div>
            ) : mqttInfo ? (
              <div style={{ fontSize: "12px", display: "grid", gridTemplateColumns: "auto 1fr", gap: "4px 8px" }}>
                <span><strong>Host:</strong></span>
                <span>{mqttInfo.host}</span>
                <span><strong>Port:</strong></span>
                <span>{mqttInfo.port}</span>
                <span><strong>Client ID:</strong></span>
                <span>{mqttInfo.client_id}</span>
                <span><strong>Status:</strong></span>
                <span style={{
                  color: mqttInfo.status === "Configured" ? "green" :
                    mqttInfo.status === "Loading..." ? "orange" : "red"
                }}>
                  {mqttInfo.status}
                </span>
              </div>
            ) : (
              <div style={{ fontSize: "12px", color: "#666" }}>Loading MQTT info...</div>
            )}
          </div>

          {/* MQTT Logs Section */}
          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginBottom: 8, marginTop: 16 }}>
            <h4 style={{ margin: 0 }}>MQTT Logs ({mqttSearchTerm ? `${filteredMqttLogs.length}/${mqttLogs.length}` : mqttLogs.length} lines)</h4>
            <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <input
                type="text"
                placeholder="Search MQTT logs..."
                value={mqttSearchTerm}
                onChange={(e) => setMqttSearchTerm(e.target.value)}
                style={{
                  padding: "4px 8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "12px"
                }}
              />
              <label style={{ fontSize: "12px", display: "flex", alignItems: "center" }}>
                <input
                  type="checkbox"
                  checked={mqttAutoScroll}
                  onChange={(e) => setMqttAutoScroll(e.target.checked)}
                  style={{ marginRight: 4 }}
                />
                Auto-scroll
              </label>
              <button onClick={clearMqttLogs} style={{
                padding: "4px 8px",
                fontSize: "12px",
                backgroundColor: "#dc3545",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer"
              }}>Clear</button>
              <button onClick={() => {
                const ta = mqttTaRef.current;
                if (ta) {
                  ta.select();
                  document.execCommand('copy');
                }
              }}>Copy All</button>
            </div>
          </div>

          <textarea
            ref={mqttTaRef}
            readOnly
            value={filteredMqttLogs.join("\n")}
            style={{
              width: "100%",
              height: 200,
              resize: "vertical",
              whiteSpace: "pre",
              fontFamily: "ui-monospace, SFMono-Regular, Menlo, Consolas, monospace",
              fontSize: 12,
              lineHeight: "1.4",
              backgroundColor: "#0d1117",
              color: "#58a6ff",
              border: "1px solid #30363d",
              borderRadius: "4px",
              padding: "8px",
            }}
            placeholder="MQTT logs will appear here..."
          />
        </div>

        <div className="card">
          {/* Influx Info Section */}
          <div style={{
            marginBottom: 12,
            padding: 8,
            backgroundColor: "#f5f5f5",
            borderRadius: 4,
            border: "1px solid #ddd",
            color: 'black'
          }}>
            <h4 style={{ margin: "0 0 8px 0", fontSize: "14px" }}>Influx Info</h4>
            {influxError ? (
              <div style={{ color: "red", fontSize: "12px" }}>{influxError}</div>
            ) : influxInfo ? (
              <div style={{ fontSize: "12px", display: "grid", gridTemplateColumns: "auto 1fr", gap: "4px 8px" }}>
                <span><strong>URL:</strong></span>
                <span>{influxInfo.Url}</span>
                <span><strong>Organization:</strong></span>
                <span>{influxInfo.Organization}</span>
                <span><strong>Status:</strong></span>
                <span style={{
                  color: influxInfo.status === "Configured" ? "green" :
                    influxInfo.status === "Loading..." ? "orange" : "red"
                }}>
                  {influxInfo.status}
                </span>
              </div>
            ) : (
              <div style={{ fontSize: "12px", color: "#666" }}>Loading Influxdb info...</div>
            )}
          </div>

          {/* Influx Logs Section */}
          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginBottom: 8, marginTop: 16 }}>
            <h4 style={{ margin: 0 }}>Influx Logs ({influxSearchTerm ? `${filteredInfluxLogs.length}/${influxLogs.length}` : influxLogs.length} lines)</h4>
            <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <input
                type="text"
                placeholder="Search Influx logs..."
                value={influxSearchTerm}
                onChange={(e) => setInfluxSearchTerm(e.target.value)}
                style={{
                  padding: "4px 8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "12px"
                }}
              />
              <label style={{ fontSize: "12px", display: "flex", alignItems: "center" }}>
                <input
                  type="checkbox"
                  checked={influxAutoScroll}
                  onChange={(e) => setInfluxAutoScroll(e.target.checked)}
                  style={{ marginRight: 4 }}
                />
                Auto-scroll
              </label>
              <button onClick={clearInfluxLogs} style={{
                padding: "4px 8px",
                fontSize: "12px",
                backgroundColor: "#dc3545",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer"
              }}>Clear</button>
              <button onClick={() => {
                const ta = influxTaRef.current;
                if (ta) {
                  ta.select();
                  document.execCommand('copy');
                }
              }}>Copy All</button>
            </div>
          </div>

          <textarea
            ref={influxTaRef}
            readOnly
            value={filteredInfluxLogs.join("\n")}
            style={{
              width: "100%",
              height: 200,
              resize: "vertical",
              whiteSpace: "pre",
              fontFamily: "ui-monospace, SFMono-Regular, Menlo, Consolas, monospace",
              fontSize: 12,
              lineHeight: "1.4",
              backgroundColor: "#0d1117",
              color: "#58a6ff",
              border: "1px solid #30363d",
              borderRadius: "4px",
              padding: "8px",
            }}
            placeholder="Influx logs will appear here..."
          />
        </div>
        
        <div className="card">
          {/* Server Logs Section */}
          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginBottom: 8 }}>
            <h4 style={{ margin: 0 }}>Server Logs ({searchTerm ? `${filteredLogs.length}/${logs.length}` : logs.length} lines)</h4>
            <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <input
                type="text"
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  padding: "4px 8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  fontSize: "12px"
                }}
              />
              <label style={{ fontSize: "12px", display: "flex", alignItems: "center" }}>
                <input
                  type="checkbox"
                  checked={autoScroll}
                  onChange={(e) => setAutoScroll(e.target.checked)}
                  style={{ marginRight: 4 }}
                />
                Auto-scroll
              </label>
              <button onClick={clearLogs}>Clear</button>
              <button onClick={() => {
                const ta = taRef.current;
                if (ta) {
                  ta.select();
                  document.execCommand('copy');
                }
              }}>Copy All</button>
            </div>
          </div>

          <textarea
            ref={taRef}
            readOnly
            value={filteredLogs.join("\n")}
            style={{
              width: "100%",
              height: 200,
              resize: "vertical",
              whiteSpace: "pre",
              fontFamily: "ui-monospace, SFMono-Regular, Menlo, Consolas, monospace",
              fontSize: 12,
              lineHeight: "1.4",
              backgroundColor: "#1e1e1e",
              color: "#d4d4d4",
              border: "1px solid #444",
              borderRadius: "4px",
              padding: "8px",
            }}
            placeholder="Server logs will appear here..."
          />
        </div>
      </main>



    </div>
  );
}
