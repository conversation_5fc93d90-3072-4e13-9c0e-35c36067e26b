package supportsvc

import (
	"context"
	"fmt"
	"reflect"
	"time"

	"github.com/traefik/yaegi/interp"
	"github.com/traefik/yaegi/stdlib"
)

// runGoScript chạy mã Go do client gửi (yaegi).
// Kỳ vọng script định nghĩa: package main; func Run(rows []map[string]interface{}) (interface{}, error)
func runGoScript(ctx context.Context, code string, rows []map[string]any, timeout time.Duration) (any, error) {
	// ép kiểu cho chắc (yaegi dùng interface{})
	in := make([]map[string]interface{}, len(rows))
	for i, r := range rows {
		in[i] = map[string]interface{}(r)
	}

	// optional timeout
	if timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	}

	done := make(chan struct{})
	var out any
	var runErr error

	go func() {
		defer close(done)

		// 1) tạo interpreter + stdlib
		i := interp.New(interp.Options{})
		i.Use(stdlib.Symbols) // CHÚ Ý: Cho phép import chuẩn; nếu cần sandbox, đừng Use toàn bộ.

		// 2) nạp code người dùng
		if _, err := i.Eval(code); err != nil {
			runErr = fmt.Errorf("compile: %w", err)
			return
		}

		// 3) lấy symbol hàm Run
		v, err := i.Eval("main.Run")
		if err != nil {
			runErr = fmt.Errorf("missing main.Run: %w", err)
			return
		}

		// 4) gọi Run(rows)
		//  - không thể assert trực tiếp đôi khi do boundary, gọi qua reflect an toàn hơn
		fn := v.Interface()
		rv := reflect.ValueOf(fn)
		if rv.Kind() != reflect.Func {
			runErr = fmt.Errorf("main.Run is not a function")
			return
		}
		args := []reflect.Value{reflect.ValueOf(in)}
		rets := rv.Call(args)
		// kỳ vọng (interface{}, error)
		if len(rets) != 2 {
			runErr = fmt.Errorf("main.Run must return (interface{}, error)")
			return
		}
		if !rets[1].IsNil() {
			runErr = rets[1].Interface().(error)
			return
		}
		out = rets[0].Interface()
	}()

	select {
	case <-ctx.Done():
		// yaegi không có cancel nội bộ, nhưng ta thôi chờ.
		if runErr != nil {
			return nil, runErr
		}
		return nil, fmt.Errorf("go script canceled/timeout: %w", ctx.Err())
	case <-done:
		return out, runErr
	}
}
