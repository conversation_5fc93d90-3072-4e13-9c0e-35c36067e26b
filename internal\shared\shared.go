package shared

import (
	"log"

	"Golang/SystemSupport/internal/config"
)

func LoadConfigSafe() *config.Config {
	paths := []string{
		"config.json",
		"../../config.json", // fallback: ở repo root
	}
	for _, p := range paths {
		if cfg, err := config.Load(p); err == nil && cfg != nil {
			log.Printf("Loaded config from %s", p)
			return cfg
		} else if err != nil {
			log.Printf("config load warning from %s: %v", p, err)
		}
	}
	log.Printf("No config.json found. Running with defaults.")
	return &config.Config{} // mặc định rỗng → MQTT nil, Influx tắt
}
