package kvstore

import (
	"context"
	"sync"
)

type Store struct {
	m  sync.Map
	qm sync.Map // key -> *queue (hàng đợi theo key)
}

type queue struct {
	mu   sync.Mutex
	data []any
	// cond *sync.Cond                 // OLD: dùng Cond dễ sai với ctx
	wait chan struct{} // NEW: kênh broadcast đánh thức waiter khi có dữ liệu
}

// New tạo một store mới (nếu không dùng Default)
func New() *Store { return &Store{} }

// ===== Instance methods =====

func (s *Store) Set(key string, value any) {
	s.m.Store(key, value)
}

func (s *Store) Get(key string) (any, bool) {
	return s.m.Load(key)
}

func (s *Store) Delete(key string) {
	s.m.Delete(key)
}

func (s *Store) LoadOrStore(key string, value any) (actual any, loaded bool) {
	return s.m.LoadOrStore(key, value)
}

// Keys trả về toàn bộ key hiện có (thứ tự không đảm bảo)
func (s *Store) Keys() []string {
	keys := make([]string, 0, 16)
	s.m.Range(func(k, _ any) bool {
		if ks, ok := k.(string); ok {
			keys = append(keys, ks)
		}
		return true
	})
	return keys
}

// Snapshot copy toàn bộ data ra map thường (để debug/inspect)
func (s *Store) Snapshot() map[string]any {
	out := make(map[string]any, 16)
	s.m.Range(func(k, v any) bool {
		if ks, ok := k.(string); ok {
			out[ks] = v
		}
		return true
	})
	return out
}

// Clear xóa toàn bộ key
func (s *Store) Clear() {
	s.m.Range(func(k, _ any) bool {
		s.m.Delete(k)
		return true
	})
	// clear luôn phần queue
	s.qm.Range(func(k, _ any) bool {
		s.qm.Delete(k)
		return true
	})
}

// ===== Typed helpers (tuỳ chọn) =====

func (s *Store) GetString(key string) (string, bool) {
	v, ok := s.Get(key)
	if !ok {
		return "", false
	}
	str, ok := v.(string)
	return str, ok
}

func (s *Store) GetInt(key string) (int, bool) {
	v, ok := s.Get(key)
	if !ok {
		return 0, false
	}
	i, ok := v.(int)
	return i, ok
}

func (s *Store) GetBool(key string) (bool, bool) {
	v, ok := s.Get(key)
	if !ok {
		return false, false
	}
	b, ok := v.(bool)
	return b, ok
}

func (s *Store) GetFloat64(key string) (float64, bool) {
	v, ok := s.Get(key)
	if !ok {
		return 0, false
	}
	f, ok := v.(float64)
	return f, ok
}

// ===== Hàng đợi theo key (key -> slice) =====
// QGetWait: block tới khi có phần tử hoặc ctx bị hủy
func (s *Store) QGetWait(ctx context.Context, key string) (any, bool) { // UPDATED: logic chờ bằng channel
	q := s.getOrCreateQueue(key)

	for {
		q.mu.Lock()
		// Có dữ liệu -> pop-front và trả
		if n := len(q.data); n > 0 {
			v := q.data[0]
			copy(q.data[0:], q.data[1:])
			q.data = q.data[:n-1]
			q.mu.Unlock()
			return v, true
		}
		// Không có dữ liệu -> tạo (hoặc lấy) kênh wait
		if q.wait == nil {
			q.wait = make(chan struct{}) // NEW
		}
		ch := q.wait
		q.mu.Unlock()

		// Chờ có dữ liệu hoặc ctx bị hủy
		select { // NEW
		case <-ctx.Done():
			return nil, false
		case <-ch:
			// được đánh thức -> loop lại kiểm tra và pop
		}
	}
}

// QAdd thêm phần tử vào cuối hàng đợi của key
func (s *Store) QAdd(key string, value any) { // UPDATED: broadcast khi từ rỗng -> có dữ liệu
	q := s.getOrCreateQueue(key)
	q.mu.Lock()
	wasEmpty := len(q.data) == 0 // NEW
	q.data = append(q.data, value)
	if wasEmpty && q.wait != nil { // NEW: đánh thức toàn bộ waiter
		close(q.wait) // broadcast
		q.wait = nil  // reset kênh cho lần chờ tiếp theo
	}
	q.mu.Unlock()
}

// QGet lấy phần tử đầu tiên từ hàng đợi của key rồi remove khỏi slice
// Trả về (val, true) nếu có phần tử; ngược lại (nil, false)
func (s *Store) QGet(key string) (any, bool) {
	qAny, ok := s.qm.Load(key)
	if !ok {
		return nil, false
	}
	q := qAny.(*queue)

	q.mu.Lock()
	defer q.mu.Unlock()

	if len(q.data) == 0 {
		return nil, false
	}
	v := q.data[0]
	// loại bỏ phần tử đầu tiên; tối ưu bằng reslice
	copy(q.data[0:], q.data[1:])
	q.data = q.data[:len(q.data)-1]
	return v, true
}

// QPeek chỉ xem phần tử đầu tiên (không remove)
func (s *Store) QPeek(key string) (any, bool) {
	qAny, ok := s.qm.Load(key)
	if !ok {
		return nil, false
	}
	q := qAny.(*queue)
	q.mu.Lock()
	defer q.mu.Unlock()
	if len(q.data) == 0 {
		return nil, false
	}
	return q.data[0], true
}

// QLen trả về số phần tử của hàng đợi theo key
func (s *Store) QLen(key string) int {
	qAny, ok := s.qm.Load(key)
	if !ok {
		return 0
	}
	q := qAny.(*queue)
	q.mu.Lock()
	n := len(q.data)
	q.mu.Unlock()
	return n
}

// QClear xoá toàn bộ hàng đợi của một key
func (s *Store) QClear(key string) {
	qAny, ok := s.qm.Load(key)
	if !ok {
		return
	}
	q := qAny.(*queue)
	q.mu.Lock()
	q.data = nil
	q.mu.Unlock()
}

func (s *Store) getOrCreateQueue(key string) *queue {
	if qAny, ok := s.qm.Load(key); ok {
		return qAny.(*queue)
	}
	newQ := &queue{data: make([]any, 0, 8)} // UPDATED: không còn init Cond
	// newQ.cond = sync.NewCond(&newQ.mu)    // OLD
	actual, _ := s.qm.LoadOrStore(key, newQ)
	return actual.(*queue)
}

// ===== Global default store (tiện dùng ở mọi nơi) =====

var Default = New()

func Set(key string, value any)                 { Default.Set(key, value) }
func Get(key string) (any, bool)                { return Default.Get(key) }
func Delete(key string)                         { Default.Delete(key) }
func LoadOrStore(key string, v any) (any, bool) { return Default.LoadOrStore(key, v) }
func Keys() []string                            { return Default.Keys() }
func Snapshot() map[string]any                  { return Default.Snapshot() }
func Clear()                                    { Default.Clear() }
func GetString(key string) (string, bool)       { return Default.GetString(key) }
func GetInt(key string) (int, bool)             { return Default.GetInt(key) }
func GetBool(key string) (bool, bool)           { return Default.GetBool(key) }
func GetFloat64(key string) (float64, bool)     { return Default.GetFloat64(key) }

// Helpers cho queue global

func QGetWait(ctx context.Context, key string) (any, bool) { return Default.QGetWait(ctx, key) }
func QAdd(key string, value any)                           { Default.QAdd(key, value) }
func QGet(key string) (any, bool)                          { return Default.QGet(key) }
func QPeek(key string) (any, bool)                         { return Default.QPeek(key) }
func QLen(key string) int                                  { return Default.QLen(key) }
func QClear(key string)                                    { Default.QClear(key) }
